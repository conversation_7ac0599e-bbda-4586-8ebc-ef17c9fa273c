import dotenv from "dotenv";
dotenv.config();
import { serveStatic } from "@hono/node-server/serve-static";

import createApp from "@/lib/create-app";
import { auth } from "@/lib/auth";
import authRoutes from "@/routes/auth";
import upload from "@/routes/upload";
import pdfLinks from "@/routes/pdf-links";


const app = createApp();

// Handle auth routes directly with better-auth
app.on(["POST", "GET"], "/api/auth/**", (c) => {
  return auth.handler(c.req.raw);
});

const routes = [upload, pdfLinks] as const;

routes.forEach((route) => {
  app.basePath("/api").route("/", route);
});

// app.get("*", serveStatic({ root: "../client/dist" }));
// app.get("*", serveStatic({ path: "index.html", root: "../client/dist" }));

export type AppType = (typeof routes)[number];

export default app;
