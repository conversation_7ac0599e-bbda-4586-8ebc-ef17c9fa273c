import { defineEventHandler, proxyRequest } from 'h3'

export default defineEventHandler(async (event) => {
  const url = event.node.req.url
  
  // 只处理 /api 路径的请求
  if (url && url.startsWith('/api')) {
    console.log('Proxying request to auth API:', url)
    // 获取目标 URL
    const target = `${process.env.NUXT_AUTH_API_URL}${url}`
    
    try {
      // 代理请求 - 修复 onResponse 回调
      return proxyRequest(event, target, {
        headers: {
          'x-forwarded-host': process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3003'
        },
        // 正确的 onResponse 回调参数结构
        onResponse(response) {
          // 确保 response 存在
          if (!response) {
            console.error('Response object is undefined');
            return;
          }
          
          console.log('Proxy response received:', response.status);
          
          // 处理 Set-Cookie 头
          const setCookie = response.headers.get('set-cookie');
          if (setCookie) {
            console.log('Found Set-Cookie header:', setCookie);
            // 确保 cookie 设置正确
            const cookies = setCookie.split(',').map(cookie => {
              return cookie.replace(/SameSite=None;?/g, '')
                           .replace(/Secure;?/g, '');
            });
            
            // 使用正确的方法设置响应头
            response.headers.set('Set-Cookie', cookies);
          }
        }
      })
    } catch (error) {
      console.error('Proxy request failed:', error);
      throw error;
    }
  }
})
