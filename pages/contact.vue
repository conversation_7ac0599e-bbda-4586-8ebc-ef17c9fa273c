<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b-2 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <NuxtLink to="/" class="text-2xl font-black text-gray-900 uppercase tracking-wide">
              PDF<span class="bg-green-500 text-white px-1 mx-1">TO</span>LINK
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="bg-white border-2 border-gray-300 p-12">
        <h1 class="text-4xl font-black text-gray-900 mb-8 uppercase tracking-tight">
          {{ $t('footer.contact') }}
        </h1>
        
        <div class="grid md:grid-cols-2 gap-12">
          <div>
            <h2 class="text-2xl font-black text-gray-900 mb-6 uppercase tracking-wide">Get in Touch</h2>
            <p class="text-gray-700 font-medium mb-8">
              Have questions, suggestions, or need help? We'd love to hear from you.
            </p>
            
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="w-6 h-6 bg-green-500 mt-1"></div>
                <div>
                  <h3 class="text-lg font-black text-gray-900 uppercase tracking-wide">Email</h3>
                  <p class="text-gray-700 font-medium"><EMAIL></p>
                </div>
              </div>
              
              <div class="flex items-start space-x-4">
                <div class="w-6 h-6 bg-green-500 mt-1"></div>
                <div>
                  <h3 class="text-lg font-black text-gray-900 uppercase tracking-wide">Response Time</h3>
                  <p class="text-gray-700 font-medium">We typically respond within 24 hours</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-4">
                <div class="w-6 h-6 bg-green-500 mt-1"></div>
                <div>
                  <h3 class="text-lg font-black text-gray-900 uppercase tracking-wide">Support Hours</h3>
                  <p class="text-gray-700 font-medium">Monday - Friday, 9:00 AM - 6:00 PM UTC</p>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h2 class="text-2xl font-black text-gray-900 mb-6 uppercase tracking-wide">Send a Message</h2>
            <form class="space-y-6">
              <div>
                <label class="block text-sm font-black text-gray-900 uppercase tracking-wide mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  required
                  class="w-full px-4 py-3 border-2 border-gray-300 focus:border-green-500 focus:outline-none font-medium"
                  placeholder="Your Name"
                >
              </div>
              
              <div>
                <label class="block text-sm font-black text-gray-900 uppercase tracking-wide mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  required
                  class="w-full px-4 py-3 border-2 border-gray-300 focus:border-green-500 focus:outline-none font-medium"
                  placeholder="<EMAIL>"
                >
              </div>
              
              <div>
                <label class="block text-sm font-black text-gray-900 uppercase tracking-wide mb-2">
                  Subject *
                </label>
                <select class="w-full px-4 py-3 border-2 border-gray-300 focus:border-green-500 focus:outline-none font-medium">
                  <option>General Question</option>
                  <option>Technical Support</option>
                  <option>Feature Request</option>
                  <option>Bug Report</option>
                  <option>Business Inquiry</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-black text-gray-900 uppercase tracking-wide mb-2">
                  Message *
                </label>
                <textarea
                  required
                  rows="6"
                  class="w-full px-4 py-3 border-2 border-gray-300 focus:border-green-500 focus:outline-none font-medium resize-none"
                  placeholder="Tell us how we can help..."
                ></textarea>
              </div>
              
              <button
                type="submit"
                class="w-full px-6 py-4 bg-green-500 text-white font-black uppercase tracking-wide hover:bg-green-600 transition-colors"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
        
        <div class="mt-16 pt-12 border-t-2 border-gray-300">
          <h2 class="text-2xl font-black text-gray-900 mb-6 uppercase tracking-wide text-center">
            Frequently Asked Questions
          </h2>
          <div class="grid md:grid-cols-2 gap-8">
            <div>
              <h3 class="text-lg font-black text-gray-900 mb-3 uppercase tracking-wide">
                How do I delete my uploaded files?
              </h3>
              <p class="text-gray-700 font-medium">
                Anonymous uploads are automatically deleted after 24 hours. If you're signed in, you can manage your files from your dashboard.
              </p>
            </div>
            
            <div>
              <h3 class="text-lg font-black text-gray-900 mb-3 uppercase tracking-wide">
                Can I upload files larger than 100MB?
              </h3>
              <p class="text-gray-700 font-medium">
                Currently, we have a 100MB limit per file. Contact us if you need to upload larger files for business use.
              </p>
            </div>
            
            <div>
              <h3 class="text-lg font-black text-gray-900 mb-3 uppercase tracking-wide">
                Is there an API available?
              </h3>
              <p class="text-gray-700 font-medium">
                We're working on an API for developers. Contact us if you're interested in early access.
              </p>
            </div>
            
            <div>
              <h3 class="text-lg font-black text-gray-900 mb-3 uppercase tracking-wide">
                Do you offer enterprise solutions?
              </h3>
              <p class="text-gray-700 font-medium">
                Yes! We offer custom solutions for businesses. Contact us to discuss your requirements.
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t-4 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <div class="flex items-center justify-center mb-4">
            <h3 class="text-2xl font-black text-white uppercase tracking-wide">
              PDF<span class="bg-green-500 text-gray-900 px-1 mx-1">TO</span>LINK
            </h3>
          </div>
          <div class="flex justify-center space-x-8 text-sm text-gray-300 mb-4">
            <NuxtLink to="/privacy" class="hover:text-green-400 font-bold uppercase tracking-wide">{{ $t('footer.privacy') }}</NuxtLink>
            <NuxtLink to="/terms" class="hover:text-green-400 font-bold uppercase tracking-wide">{{ $t('footer.terms') }}</NuxtLink>
            <NuxtLink to="/contact" class="hover:text-green-400 font-bold uppercase tracking-wide">{{ $t('footer.contact') }}</NuxtLink>
          </div>
          <div class="text-sm text-gray-400">
            <p class="font-medium">&copy; 2024 PDFtoLink. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
const { t } = useI18n()

// SEO Meta
useHead({
  title: computed(() => `${t('footer.contact')} - PDFtoLink`),
  meta: [
    {
      name: 'description',
      content: 'Contact PDFtoLink for support, questions, or business inquiries. Get help with converting your PDF files to shareable links.'
    }
  ]
})
</script>