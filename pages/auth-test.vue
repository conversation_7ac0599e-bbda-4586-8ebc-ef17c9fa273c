<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="max-w-md w-full bg-white rounded-lg shadow p-6">
      <h1 class="text-2xl font-bold text-center mb-6">Auth Test</h1>
      
      <div v-if="status === 'loading'" class="text-center">
        <p>Loading...</p>
      </div>
      
      <div v-else-if="status === 'authenticated' && user" class="space-y-4">
        <div class="text-center">
          <img :src="user.image" :alt="user.name" class="w-16 h-16 rounded-full mx-auto mb-2">
          <h2 class="text-lg font-semibold">{{ user.name }}</h2>
          <p class="text-gray-600">{{ user.email }}</p>
          <p class="text-sm text-gray-500">ID: {{ user.id }}</p>
        </div>
        <button
          @click="handleSignOut"
          class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Sign Out
        </button>
      </div>
      
      <div v-else class="space-y-4">
        <p class="text-center text-gray-600">Not signed in</p>
        <button
          @click="handleSignIn"
          class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Sign In with Google
        </button>
      </div>
      
      <div class="mt-6 pt-4 border-t">
        <h3 class="font-semibold mb-2">Debug Info:</h3>
        <pre class="text-xs bg-gray-100 p-2 rounded overflow-auto">{{ {
          status,
          hasData: !!data,
          hasUser: !!user,
          userData: user || null,
          rawData: data,
          isPending,
          error
        } }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
const { data, status, user, error, isPending, signIn, signOut } = useAuth()

const handleSignIn = () => {
  signIn('google')
}

const handleSignOut = () => {
  signOut()
}

useHead({
  title: 'Auth Test - PDFtoLink'
})
</script>