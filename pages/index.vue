<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b-2 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <h1 class="text-2xl font-black text-gray-900 uppercase tracking-wide">
              PDF<span class="bg-green-500 text-white px-1 mx-1">TO</span>LINK
            </h1>
          </div>
          
          <div class="flex items-center space-x-6">
            <!-- Language Selector -->
            <div class="relative">
              <select
                @change="switchLanguage"
                class="appearance-none bg-white border-2 border-gray-300 px-4 py-2 text-sm font-medium focus:outline-none focus:border-green-500"
              >
                <option value="en" :selected="locale === 'en'">English</option>
                <option value="zh-hans" :selected="locale === 'zh-hans'">简体中文</option>
                <option value="zh-hant" :selected="locale === 'zh-hant'">繁體中文</option>
              </select>
            </div>
            
            <!-- Auth Button -->
            <div v-if="status === 'loading'" class="text-sm text-gray-600 font-medium">
              Loading...
            </div>
            
            <button
              v-else-if="status === 'unauthenticated'"
              @click="() => signIn('google')"
              class="px-6 py-2 bg-green-500 text-white font-bold uppercase tracking-wide hover:bg-green-600 transition-colors"
            >
              {{ t('auth.signInWithGoogle') }}
            </button>
            
            <div v-else-if="status === 'authenticated' && user" class="relative">
              <button
                @click="toggleDropdown"
                class="flex items-center space-x-3 text-sm border-2 border-gray-300 px-4 py-2 hover:border-green-500 transition-colors"
                :aria-expanded="isDropdownOpen"
                aria-haspopup="true"
              >
                <div class="w-6 h-6 bg-green-500"></div>
                <span class="hidden md:block text-gray-900 font-medium">{{ user.name }}</span>
                <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div
                v-show="isDropdownOpen"
                @click="closeDropdown"
                class="absolute right-0 mt-2 w-64 bg-white border-2 border-gray-300 z-50"
                role="menu"
                aria-orientation="vertical"
              >
                <div role="none">
                  <div class="px-6 py-4 text-sm text-gray-900 border-b border-gray-200">
                    <div class="font-bold">{{ user.name }}</div>
                    <div class="text-gray-600">{{ user.email }}</div>
                  </div>
                  
                  <NuxtLink
                    :to="localePath('/dashboard')"
                    class="block px-6 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 font-medium"
                    role="menuitem"
                  >
                    <div class="flex items-center">
                      <div class="w-4 h-4 bg-gray-400 mr-3"></div>
                      {{ t('auth.dashboard') }}
                    </div>
                  </NuxtLink>
                  
                  <button
                    @click="handleSignOut"
                    class="block w-full text-left px-6 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 font-medium"
                    role="menuitem"
                  >
                    <div class="flex items-center">
                      <div class="w-4 h-4 bg-gray-400 mr-3"></div>
                      {{ t('auth.signOut') }}
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Hero Section -->
      <div class="text-center mb-20">
        <h1 class="text-5xl font-black text-gray-900 mb-6 uppercase tracking-tight leading-tight">
          <div class="mb-2">PDF <span class="bg-green-500 text-white px-2">TO</span> LINK</div>
          <div class="text-3xl text-gray-700">{{ t('slogan').toUpperCase() }}</div>
        </h1>
        <p class="text-xl text-gray-700 max-w-3xl mx-auto font-medium leading-relaxed">
          {{ t('subtitle') }}
        </p>
        
        <!-- Stats -->
        <div class="flex justify-center items-center space-x-12 mt-12">
          <div class="text-center">
            <div class="text-3xl font-black text-gray-900">12,736</div>
            <div class="text-sm font-bold text-gray-600 uppercase tracking-wide">PDFs Uploaded</div>
          </div>
          <div class="w-1 h-12 bg-gray-300"></div>
          <div class="text-center">
            <div class="text-3xl font-black text-gray-900">1M+</div>
            <div class="text-sm font-bold text-gray-600 uppercase tracking-wide">Views</div>
          </div>
        </div>
      </div>

      <!-- Upload Area -->
      <div class="bg-white border-2 border-gray-300 p-12 mb-20">
        <div
          @drop.prevent="handleFileDrop"
          @dragover.prevent
          @dragenter.prevent
          @click="triggerFileInput"
          class="border-4 border-dashed border-gray-300 p-16 text-center cursor-pointer transition-colors hover:border-green-500 hover:bg-green-50"
          :class="{ 'border-green-500 bg-green-50': isDragging }"
        >

          
          <h3 class="text-2xl font-black text-white bg-green-500 px-6 py-3 mb-6 uppercase tracking-wide inline-block">
            {{ t('uploadArea.title') }}
          </h3>
          <p class="text-lg text-gray-600 mb-8 font-medium">
            {{ t('uploadArea.subtitle') }}
          </p>
          
          <div class="text-sm text-gray-500 space-y-2 font-medium">
            <p>{{ t('uploadArea.supportedFormat') }}</p>
            <p>{{ t('uploadArea.maxSize') }}</p>
          </div>
        </div>
        
        <input
          ref="fileInput"
          type="file"
          accept=".pdf"
          multiple
          @change="handleFileSelect"
          class="hidden"
        >
        
        <!-- Upload Progress -->
        <div v-if="uploadProgress.length > 0" class="mt-8 space-y-4">
          <div v-for="file in uploadProgress" :key="file.id" class="bg-gray-100 border-2 border-gray-300 p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-bold text-gray-900 uppercase">{{ file.name }}</span>
              <span class="text-sm text-gray-600 font-bold">{{ file.progress }}%</span>
            </div>
            <div class="w-full bg-gray-300 h-3">
              <div 
                class="bg-green-500 h-3 transition-all duration-300"
                :style="{ width: file.progress + '%' }"
              ></div>
            </div>
          </div>
        </div>
        
        <!-- Success Links -->
        <div v-if="generatedLinks.length > 0" class="mt-8 space-y-4">
          <h4 class="text-xl font-bold text-gray-900 uppercase tracking-wide">{{ t('uploadSuccess.title') }}</h4>
          <div v-for="link in generatedLinks" :key="link.id" class="bg-green-100 border-2 border-green-500 p-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <p class="text-sm font-bold text-gray-900 uppercase mb-2">{{ link.filename }}</p>
                <p class="text-sm text-gray-600 break-all font-mono mb-3">{{ link.url }}</p>
                <div class="flex items-center space-x-4">
                  <span v-if="status === 'authenticated'" class="text-xs text-green-700 font-medium bg-green-200 px-2 py-1 rounded">
                    {{ t('uploadSuccess.expiresIn30days') }}
                  </span>
                  <span v-else class="text-xs text-orange-700 font-medium bg-orange-200 px-2 py-1 rounded">
                    {{ t('uploadSuccess.expiresIn24h') }}
                  </span>
                  <button
                    v-if="status === 'unauthenticated'"
                    @click="() => signIn('google')"
                    class="text-xs text-blue-700 hover:text-blue-800 underline font-medium"
                  >
                    {{ t('uploadSuccess.signInForLonger') }}
                  </button>
                </div>
              </div>
              <div class="flex space-x-3">
                <button
                  @click="copyToClipboard(link.url)"
                  class="px-4 py-2 bg-green-500 text-white font-bold uppercase text-xs tracking-wide hover:bg-green-600 transition-colors"
                >
                  {{ t('uploadSuccess.copyLink') }}
                </button>
                <a
                  :href="link.url"
                  target="_blank"
                  class="px-4 py-2 bg-gray-500 text-white font-bold uppercase text-xs tracking-wide hover:bg-gray-600 transition-colors"
                >
                  View
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Features Section -->
      <div class="grid md:grid-cols-3 gap-8 mb-20">
        <div class="text-center bg-white border-2 border-gray-300 p-8">
          <h3 class="text-xl font-black text-white bg-green-500 px-4 py-2 mb-6 uppercase tracking-wide">
            {{ t('features.instant') }}
          </h3>
          <p class="text-gray-700 font-medium leading-relaxed">
            {{ t('features.instantDesc') }}
          </p>
        </div>
        
        <div class="text-center bg-white border-2 border-gray-300 p-8">
          <h3 class="text-xl font-black text-white bg-green-500 px-4 py-2 mb-6 uppercase tracking-wide">
            {{ t('features.secure') }}
          </h3>
          <p class="text-gray-700 font-medium leading-relaxed">
            {{ t('features.secureDesc') }}
          </p>
        </div>
        
        <div class="text-center bg-white border-2 border-gray-300 p-8">
          <h3 class="text-xl font-black text-white bg-green-500 px-4 py-2 mb-6 uppercase tracking-wide">
            {{ t('features.temporary') }}
          </h3>
          <p class="text-gray-700 font-medium leading-relaxed">
            {{ t('features.temporaryDesc') }}
          </p>
        </div>
      </div>

      <!-- How It Works Section -->
      <section class="bg-white border-2 border-gray-300 p-12 mb-20">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-black text-gray-900 mb-6 uppercase tracking-tight">
            {{ t('howItWorks.title') }}
          </h2>
          <p class="text-xl text-gray-700 max-w-3xl mx-auto font-medium leading-relaxed">
            {{ t('howItWorks.subtitle') }}
          </p>
        </div>

        <div class="max-w-5xl mx-auto">
          <div class="grid md:grid-cols-3 gap-12">
            <div 
              v-for="(step, index) in howItWorksSteps" 
              :key="index"
              class="text-center border-2 border-gray-300 p-8"
            >
              <div class="mb-8">
                <div class="w-16 h-16 bg-green-500 flex items-center justify-center mx-auto">
                  <span class="text-2xl font-black text-white">{{ index + 1 }}</span>
                </div>
              </div>
              <h3 class="text-2xl font-black text-gray-900 mb-4 uppercase tracking-wide">
                {{ step.title }}
              </h3>
              <p class="text-gray-700 leading-relaxed font-medium">
                {{ step.description }}
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Benefits Section -->
      <section class="bg-green-100 border-2 border-green-500 p-12 mb-20">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-black text-gray-900 mb-6 uppercase tracking-tight">
            {{ t('benefits.title') }}
          </h2>
          <p class="text-xl text-gray-700 max-w-3xl mx-auto font-medium leading-relaxed">
            {{ t('benefits.subtitle') }}
          </p>
        </div>

        <div class="max-w-5xl mx-auto">
          <div class="grid md:grid-cols-2 gap-8">
            <div 
              v-for="(benefit, index) in benefitsItems" 
              :key="index"
              class="bg-white border-2 border-gray-300 p-8"
            >
              <div class="flex items-start space-x-6">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-500"></div>
                </div>
                <div class="flex-1">
                  <h3 class="text-xl font-black text-gray-900 mb-4 uppercase tracking-wide">
                    {{ benefit.title }}
                  </h3>
                  <p class="text-gray-700 leading-relaxed font-medium">
                    {{ benefit.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- FAQ Section -->
      <section class="bg-white border-2 border-gray-300 p-12 mb-20">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-black text-gray-900 mb-6 uppercase tracking-tight">
            {{ t('faq.title') }}
          </h2>
          <p class="text-xl text-gray-700 max-w-3xl mx-auto font-medium leading-relaxed">
            {{ t('faq.subtitle') }}
          </p>
        </div>

        <div class="max-w-4xl mx-auto">
          <div 
            v-for="(faq, index) in faqQuestions" 
            :key="index"
            class="border-b-2 border-gray-300 last:border-b-0"
            itemscope
            itemtype="https://schema.org/Question"
          >
            <button
              @click="toggleFAQ(index)"
              class="w-full py-8 text-left focus:outline-none hover:bg-gray-50 px-6"
              :aria-expanded="openFAQs.has(index)"
              :aria-controls="`faq-answer-${index}`"
            >
              <div class="flex items-center justify-between">
                <h3 
                  class="text-xl font-black text-gray-900 pr-6 uppercase tracking-wide"
                  itemprop="name"
                >
                  {{ faq.question }}
                </h3>
                <div class="flex-shrink-0">
                  <div 
                    class="w-6 h-6 bg-green-500 flex items-center justify-center transition-transform duration-200"
                    :class="{ 'rotate-180': openFAQs.has(index) }"
                  >
                    <svg 
                      class="w-4 h-4 text-white"
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </button>
            
            <div 
              :id="`faq-answer-${index}`"
              class="overflow-hidden transition-all duration-300 ease-in-out"
              :class="{
                'max-h-96 pb-8': openFAQs.has(index),
                'max-h-0': !openFAQs.has(index)
              }"
              itemscope
              itemtype="https://schema.org/Answer"
            >
              <div 
                class="text-gray-700 leading-relaxed font-medium px-6"
                itemprop="text"
              >
                {{ faq.answer }}
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="bg-gray-50 border-2 border-gray-300 p-16 mb-20">
        <div class="text-center">
          <h2 class="text-4xl font-black text-gray-900 mb-6 uppercase tracking-tight">
            Ready to Share Your PDFs?
          </h2>
          <p class="text-xl text-gray-700 max-w-2xl mx-auto font-medium leading-relaxed mb-10">
            Join thousands of users who trust PDFtoLink for secure, instant PDF sharing. Upload your first file now!
          </p>
          <div class="flex justify-center space-x-6">
            <button
              @click="triggerFileInput"
              class="px-8 py-4 bg-green-500 text-white font-black uppercase tracking-wide hover:bg-green-600 transition-colors text-lg"
            >
              Upload PDF Now
            </button>
            <button
              v-if="status === 'unauthenticated'"
              @click="() => signIn('google')"
              class="px-8 py-4 border-2 border-green-500 text-green-500 font-black uppercase tracking-wide hover:bg-green-500 hover:text-white transition-colors text-lg"
            >
              Sign Up Free
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t-4 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <!-- Logo Section -->
        <div class="text-center mb-12">
          <div class="flex items-center justify-center mb-4">
            <h3 class="text-3xl font-black text-white uppercase tracking-wide">
              PDF<span class="bg-green-500 text-gray-900 px-1 mx-1">TO</span>LINK
            </h3>
          </div>
          <p class="text-gray-400 font-medium max-w-2xl mx-auto">
            {{ t('subtitle') }}
          </p>
        </div>

        <!-- Links Grid -->
        <div class="grid md:grid-cols-4 gap-8 mb-12">
          <!-- Product -->
          <div>
            <h4 class="text-lg font-black text-white uppercase tracking-wide mb-4">Product</h4>
            <ul class="space-y-3">
              <li><NuxtLink to="/" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Home</NuxtLink></li>
              <li><NuxtLink to="/dashboard" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Dashboard</NuxtLink></li>
              <li><NuxtLink to="#features" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Features</NuxtLink></li>
              <li><NuxtLink to="#pricing" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Pricing</NuxtLink></li>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h4 class="text-lg font-black text-white uppercase tracking-wide mb-4">Support</h4>
            <ul class="space-y-3">
              <li><NuxtLink :to="localePath('/contact')" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">{{ t('footer.contact') }}</NuxtLink></li>
              <li><a href="#faq" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">FAQ</a></li>
              <li><a href="mailto:<EMAIL>" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Help Center</a></li>
              <li><a href="#status" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Status</a></li>
            </ul>
          </div>

          <!-- Company -->
          <div>
            <h4 class="text-lg font-black text-white uppercase tracking-wide mb-4">Company</h4>
            <ul class="space-y-3">
              <li><a href="#about" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">About</a></li>
              <li><a href="#blog" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Blog</a></li>
              <li><a href="#careers" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Careers</a></li>
              <li><a href="#press" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Press Kit</a></li>
            </ul>
          </div>

          <!-- Legal -->
          <div>
            <h4 class="text-lg font-black text-white uppercase tracking-wide mb-4">Legal</h4>
            <ul class="space-y-3">
              <li><NuxtLink :to="localePath('/privacy')" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">{{ t('footer.privacy') }}</NuxtLink></li>
              <li><NuxtLink :to="localePath('/terms')" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">{{ t('footer.terms') }}</NuxtLink></li>
              <li><a href="#cookies" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Cookies</a></li>
              <li><a href="#security" class="text-gray-300 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Security</a></li>
            </ul>
          </div>
        </div>

        <!-- Bottom Section -->
        <div class="border-t border-gray-700 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="text-sm text-gray-400 mb-4 md:mb-0">
              <p class="font-medium">&copy; 2024 PDFtoLink. All rights reserved.</p>
            </div>
            <div class="flex space-x-6">
              <a href="#twitter" class="text-gray-400 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Twitter</a>
              <a href="#github" class="text-gray-400 hover:text-green-400 font-medium uppercase text-sm tracking-wide">GitHub</a>
              <a href="#discord" class="text-gray-400 hover:text-green-400 font-medium uppercase text-sm tracking-wide">Discord</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
const { locale, locales, t, tm } = useI18n()
const switchLocalePath = useSwitchLocalePath()
const localePath = useLocalePath()
const { data, status, user, signIn, signOut } = useAuth()

// Dropdown menu state
const isDropdownOpen = ref(false)

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

const handleSignOut = async () => {
  closeDropdown()
  await signOut()
}

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (event) => {
    const dropdownElement = event.target.closest('.relative')
    if (!dropdownElement) {
      isDropdownOpen.value = false
    }
  })
})

// File upload state
const fileInput = ref(null)
const isDragging = ref(false)
const uploadProgress = ref([])
const generatedLinks = ref([])

// FAQ state
const openFAQs = ref(new Set())

// Static data for sections since i18n complex arrays have issues
const howItWorksSteps = computed(() => {
  const currentLocale = locale.value
  
  if (currentLocale === 'zh-hans') {
    return [
      {
        title: "上传您的 PDF",
        description: "拖放您的 PDF 文件或点击浏览。支持最大 100MB 的文件。"
      },
      {
        title: "获取即时链接",
        description: "您的 PDF 安全上传后，会立即生成唯一的可分享链接。"
      },
      {
        title: "随处分享",
        description: "复制链接并通过电子邮件、社交媒体分享，或嵌入到您的网站中。"
      }
    ]
  } else if (currentLocale === 'zh-hant') {
    return [
      {
        title: "上傳您的 PDF",
        description: "拖放您的 PDF 檔案或點擊瀏覽。支援最大 100MB 的檔案。"
      },
      {
        title: "獲取即時連結",
        description: "您的 PDF 安全上傳後，會立即生成唯一的可分享連結。"
      },
      {
        title: "隨處分享",
        description: "複製連結並透過電子郵件、社交媒體分享，或嵌入到您的網站中。"
      }
    ]
  } else {
    return [
      {
        title: "Upload Your PDF",
        description: "Drag and drop your PDF file or click to browse. Files up to 100MB are supported."
      },
      {
        title: "Get Instant Link",
        description: "Your PDF is securely uploaded and a unique shareable link is generated immediately."
      },
      {
        title: "Share Anywhere",
        description: "Copy the link and share it via email, social media, or embed it in your website."
      }
    ]
  }
})

const benefitsItems = computed(() => {
  const currentLocale = locale.value
  
  if (currentLocale === 'zh-hans') {
    return [
      {
        title: "无需邮件附件",
        description: "通过可分享链接绕过邮件大小限制和收件箱混乱"
      },
      {
        title: "通用访问",
        description: "接收者可以在任何设备上查看 PDF，无需特殊软件"
      },
      {
        title: "隐私优先",
        description: "自动过期和加密确保您的文档保持私密"
      },
      {
        title: "分析跟踪",
        description: "通过详细分析查看谁查看了您的 PDF 以及何时查看"
      }
    ]
  } else if (currentLocale === 'zh-hant') {
    return [
      {
        title: "無需郵件附件",
        description: "透過可分享連結繞過郵件大小限制和收件箱混亂"
      },
      {
        title: "通用存取",
        description: "接收者可以在任何裝置上檢視 PDF，無需特殊軟體"
      },
      {
        title: "隱私優先",
        description: "自動過期和加密確保您的文件保持私密"
      },
      {
        title: "分析追蹤",
        description: "透過詳細分析檢視誰檢視了您的 PDF 以及何時檢視"
      }
    ]
  } else {
    return [
      {
        title: "No Email Attachments",
        description: "Bypass email size limits and inbox clutter with shareable links"
      },
      {
        title: "Universal Access",
        description: "Recipients can view PDFs on any device without special software"
      },
      {
        title: "Privacy First",
        description: "Automatic expiration and encryption ensure your documents stay private"
      },
      {
        title: "Analytics Tracking",
        description: "See who viewed your PDFs and when with detailed analytics"
      }
    ]
  }
})

const faqQuestions = computed(() => {
  const currentLocale = locale.value
  
  if (currentLocale === 'zh-hans') {
    return [
      {
        question: "如何将 PDF 转换为可分享的链接？",
        answer: "只需将 PDF 文件拖放到上方的上传区域，或点击选择文件。您的 PDF 将被上传，并立即生成可分享的链接。基本使用无需注册。"
      },
      {
        question: "链接有效期是多长时间？",
        answer: "免费匿名链接在 24 小时后过期，以确保安全和隐私。如果您使用 Google 登录，链接将保持 30 天有效，并可在控制台中管理。"
      },
      {
        question: "上传文件的最大大小限制是多少？",
        answer: "您可以上传最大 100MB 的 PDF 文件。这涵盖了大多数文档，包括大型演示文稿、报告和电子书。"
      },
      {
        question: "我上传的 PDF 安全吗？",
        answer: "是的，您的 PDF 在传输和存储过程中都会被加密。只有拥有唯一 URL 的人才能访问链接。您还可以为敏感文档添加密码保护。"
      },
      {
        question: "我可以为共享的 PDF 设置密码保护吗？",
        answer: "是的！登录后，您可以为 PDF 添加密码保护。接收者需要输入密码才能查看文档。"
      }
    ]
  } else if (currentLocale === 'zh-hant') {
    return [
      {
        question: "如何將 PDF 轉換為可分享的連結？",
        answer: "只需將 PDF 檔案拖放到上方的上傳區域，或點擊選擇檔案。您的 PDF 將被上傳，並立即生成可分享的連結。基本使用無需註冊。"
      },
      {
        question: "連結有效期是多長時間？",
        answer: "免費匿名連結在 24 小時後過期，以確保安全和隱私。如果您使用 Google 登入，連結將保持 30 天有效，並可在控制台中管理。"
      },
      {
        question: "上傳檔案的最大大小限制是多少？",
        answer: "您可以上傳最大 100MB 的 PDF 檔案。這涵蓋了大多數文件，包括大型簡報、報告和電子書。"
      },
      {
        question: "我上傳的 PDF 安全嗎？",
        answer: "是的，您的 PDF 在傳輸和儲存過程中都會被加密。只有擁有唯一 URL 的人才能存取連結。您還可以為敏感文件添加密碼保護。"
      },
      {
        question: "我可以為共享的 PDF 設定密碼保護嗎？",
        answer: "是的！登入後，您可以為 PDF 添加密碼保護。接收者需要輸入密碼才能檢視文件。"
      }
    ]
  } else {
    return [
      {
        question: "How do I convert a PDF to a shareable link?",
        answer: "Simply drag and drop your PDF file into the upload area above, or click to select a file. Your PDF will be uploaded and a shareable link will be generated instantly. No registration required for basic use."
      },
      {
        question: "How long do the links last?",
        answer: "Free anonymous links expire after 24 hours for security and privacy. If you sign in with Google, your links will last for 30 days and you can manage them from your dashboard."
      },
      {
        question: "What's the maximum file size I can upload?",
        answer: "You can upload PDF files up to 100MB in size. This covers most documents including large presentations, reports, and e-books."
      },
      {
        question: "Is my PDF secure when I upload it?",
        answer: "Yes, your PDFs are encrypted during transmission and storage. Links are only accessible to those who have the unique URL. You can also add password protection for sensitive documents."
      },
      {
        question: "Can I password protect my shared PDFs?",
        answer: "Yes! When you're signed in, you can add password protection to your PDFs. Recipients will need to enter the password before they can view the document."
      }
    ]
  }
})

// SEO Meta with FAQ structured data
useHead({
  title: computed(() => t('title')),
  meta: [
    {
      name: 'description',
      content: computed(() => t('subtitle'))
    },
    {
      property: 'og:title',
      content: computed(() => t('title'))
    },
    {
      property: 'og:description',
      content: computed(() => t('subtitle'))
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: computed(() => {
        if (!Array.isArray(faqQuestions.value) || faqQuestions.value.length === 0) {
          return JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            'name': t('title')
          })
        }
        return JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          'mainEntity': faqQuestions.value.map((faq, index) => ({
            '@type': 'Question',
            'name': faq.question,
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': faq.answer
            }
          }))
        })
      })
    }
  ]
})

// Language switching
const switchLanguage = (event) => {
  const newLocale = event.target.value
  if (newLocale !== locale.value) {
    navigateTo(switchLocalePath(newLocale))
  }
}

// File handling
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  processFiles(files)
}

const handleFileDrop = (event) => {
  isDragging.value = false
  const files = Array.from(event.dataTransfer.files).filter(file => file.type === 'application/pdf')
  processFiles(files)
}

const processFiles = async (files) => {
  for (const file of files) {
    if (file.size > 100 * 1024 * 1024) { // 100MB limit
      alert(`File ${file.name} is too large. Max size is 100MB.`)
      continue
    }
    
    const fileId = Date.now() + Math.random()
    uploadProgress.value.push({
      id: fileId,
      name: file.name,
      progress: 0
    })
    
    try {
      const linkData = await uploadFile(file, fileId)
      generatedLinks.value.push({
        id: linkData.linkId,
        filename: file.name,
        url: linkData.shareableUrl
      })
    } catch (error) {
      console.error('Upload failed:', error)
      alert(`Failed to upload ${file.name}`)
    } finally {
      uploadProgress.value = uploadProgress.value.filter(item => item.id !== fileId)
    }
  }
}

const uploadFile = async (file, fileId) => {
  // Update progress
  const updateProgress = (progress) => {
    const item = uploadProgress.value.find(item => item.id === fileId)
    if (item) item.progress = progress
  }
  
  updateProgress(10)
  
  // Get presigned URL
  const { data: uploadData } = await $fetch('/api/upload/presign', {
    method: 'POST',
    body: {
      filename: file.name,
      contentType: file.type
    }
  })
  
  updateProgress(30)
  
  // Upload to R2
  const uploadResponse = await fetch(uploadData.uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type
    }
  })
  
  if (!uploadResponse.ok) {
    throw new Error('Upload failed')
  }
  
  updateProgress(80)
  
  // Generate PDF link
  const linkResponse = await $fetch('/api/pdf-links/generate', {
    method: 'POST',
    body: {
      fileKey: uploadData.fileKey,
      filename: file.name,
      isAuthenticated: !!data.value?.session?.user,
      // password: null, // No password for now
      // maxViews: null, // No view limit for now
      // customExpiration: null // Use default expiration
    }
  })
  
  updateProgress(100)
  
  return {
    linkId: linkResponse.data.linkId,
    shareableUrl: linkResponse.data.shareableUrl
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // You might want to show a toast notification here
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

// FAQ functionality
const toggleFAQ = (index) => {
  if (openFAQs.value.has(index)) {
    openFAQs.value.delete(index)
  } else {
    openFAQs.value.add(index)
  }
}

// Drag and drop event handlers
onMounted(() => {
  document.addEventListener('dragenter', (e) => {
    e.preventDefault()
    isDragging.value = true
  })
  
  document.addEventListener('dragleave', (e) => {
    e.preventDefault()
    if (!e.relatedTarget) {
      isDragging.value = false
    }
  })
  
  document.addEventListener('dragover', (e) => {
    e.preventDefault()
  })
  
  document.addEventListener('drop', (e) => {
    e.preventDefault()
    isDragging.value = false
  })
})
</script>
