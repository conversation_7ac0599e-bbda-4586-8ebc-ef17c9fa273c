<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b-2 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <NuxtLink to="/" class="flex items-center">
            <h1 class="text-2xl font-black text-gray-900 uppercase tracking-wide">
              PDF<span class="bg-green-500 text-white px-1 mx-1">TO</span>LINK
            </h1>
          </NuxtLink>
          
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/"
              class="px-6 py-2 bg-green-500 text-white font-bold uppercase tracking-wide hover:bg-green-600 transition-colors"
            >
              Upload New PDF
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="pending" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        <p class="mt-2 text-gray-600">Loading PDF...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
            <svg class="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-red-900 mb-2">{{ error.statusCode === 404 ? 'PDF Not Found' : 'Error Loading PDF' }}</h3>
          <p class="text-red-700 mb-4">{{ error.statusMessage || 'The PDF you\'re looking for could not be found or has expired.' }}</p>
          <NuxtLink
            to="/"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Upload New PDF
          </NuxtLink>
        </div>
      </div>

      <!-- Password Dialog -->
      <div v-if="showPasswordDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 rounded-md bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full">
              <svg class="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-1a2 2 0 00-2-2H6a2 2 0 00-2 2v1a2 2 0 002 2zM10 6V4a2 2 0 114 0v2m-7 7h8a2 2 0 002-2V8a2 2 0 00-2-2H9a2 2 0 00-2 2v3a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 text-center mt-4">Password Protected</h3>
            <p class="text-sm text-gray-500 text-center mt-2">This PDF is password protected. Please enter the password to continue.</p>
            
            <div class="mt-4">
              <input
                v-model="password"
                type="password"
                placeholder="Enter password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                @keyup.enter="verifyPassword"
              >
              
              <p v-if="passwordError" class="text-red-600 text-sm mt-2">{{ passwordError }}</p>
            </div>
            
            <div class="flex space-x-3 mt-6">
              <button
                @click="$router.push('/')"
                class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                @click="verifyPassword"
                :disabled="isVerifying || !password"
                class="flex-1 px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="isVerifying">Verifying...</span>
                <span v-else>Access PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- PDF Viewer -->
      <div v-else-if="data && data.success">
        <PdfViewer
          :download-url="data.data.downloadUrl"
          :filename="data.data.filename"
          :views="data.data.views"
          :expires-at="data.data.expiresAt"
          :share-url="currentUrl"
        />
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <p class="text-sm text-gray-500">
            Powered by 
            <NuxtLink to="/" class="text-green-600 hover:underline font-medium">PDFtoLink</NuxtLink>
            - Share your PDFs instantly
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
const route = useRoute()
const linkId = route.params.id

const runtimeConfig = useRuntimeConfig()

// State for password protection
const showPasswordDialog = ref(false)
const password = ref('')
const passwordError = ref('')
const isVerifying = ref(false)

// Fetch PDF data
const { data, pending, error, refresh } = await useFetch(`${runtimeConfig.public.apiUrl}/api/pdf-links/${linkId}`)

console.log('data.value', data.value)

// Check if password is required
watch(data, (newData) => {
  if (newData && !newData.success && newData.requiresPassword) {
    showPasswordDialog.value = true
  }
}, { immediate: true })

// Get current URL for sharing
const currentUrl = ref('')
onMounted(() => {
  currentUrl.value = window.location.href
})

// SEO and meta tags
useHead({
  title: () => {
    if (data.value?.filename) {
      return `${data.value.filename} - PDFtoLink`
    }
    return 'View PDF - PDFtoLink'
  },
  meta: [
    {
      name: 'description',
      content: () => {
        if (data.value?.filename) {
          return `View and download ${data.value.filename} - shared via PDFtoLink`
        }
        return 'View PDF document shared via PDFtoLink'
      }
    },
    {
      property: 'og:title',
      content: () => {
        if (data.value?.filename) {
          return `${data.value.filename} - PDFtoLink`
        }
        return 'View PDF - PDFtoLink'
      }
    },
    {
      property: 'og:description',
      content: () => {
        if (data.value?.filename) {
          return `View and download ${data.value.filename} - shared via PDFtoLink`
        }
        return 'View PDF document shared via PDFtoLink'
      }
    },
    {
      property: 'og:type',
      content: 'article'
    },
    {
      name: 'robots',
      content: 'noindex, nofollow' // Don't index shared PDF pages
    }
  ]
})

// Password verification
const verifyPassword = async () => {
  if (!password.value) return
  
  isVerifying.value = true
  passwordError.value = ''
  
  try {
    const verification = await $fetch(`${runtimeConfig.public.apiUrl}/api/pdf-links/verify`, {
      method: 'POST',
      body: {
        linkId,
        password: password.value
      }
    })
    
    if (verification.success) {
      showPasswordDialog.value = false
      // Refresh the main data to get the actual PDF
      await refresh()
    }
  } catch (error) {
    passwordError.value = error.data?.message || 'Invalid password'
  } finally {
    isVerifying.value = false
  }
}

// Handle 404 and other errors
if (error.value) {
  throw createError({
    statusCode: error.value.statusCode || 404,
    statusMessage: error.value.statusMessage || 'PDF not found'
  })
}
</script>