<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b-2 border-green-500">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <NuxtLink :to="localePath('/')" class="text-2xl font-black text-gray-900 uppercase tracking-wide">
              PDF<span class="bg-green-500 text-white px-1 mx-1">TO</span>LINK
            </NuxtLink>
          </div>
          
          <div class="flex items-center space-x-4">
            <div v-if="user" class="relative">
              <button
                @click="toggleDropdown"
                class="flex items-center space-x-3 text-sm border-2 border-gray-300 px-4 py-2 hover:border-green-500 transition-colors"
                :aria-expanded="isDropdownOpen"
                aria-haspopup="true"
              >
                <div class="w-6 h-6 bg-green-500"></div>
                <span class="hidden md:block text-gray-900 font-medium">{{ user.name }}</span>
                <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div
                v-show="isDropdownOpen"
                @click="closeDropdown"
                class="absolute right-0 mt-2 w-64 bg-white border-2 border-gray-300 z-50"
                role="menu"
                aria-orientation="vertical"
              >
                <div role="none">
                  <div class="px-6 py-4 text-sm text-gray-900 border-b border-gray-200">
                    <div class="font-bold">{{ user.name }}</div>
                    <div class="text-gray-600">{{ user.email }}</div>
                  </div>
                  
                  <NuxtLink
                    :to="localePath('/')"
                    class="block px-6 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 font-medium"
                    role="menuitem"
                  >
                    <div class="flex items-center">
                      <div class="w-4 h-4 bg-gray-400 mr-3"></div>
                      {{ t('home') }}
                    </div>
                  </NuxtLink>
                  
                  <button
                    @click="handleSignOut"
                    class="block w-full text-left px-6 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 font-medium"
                    role="menuitem"
                  >
                    <div class="flex items-center">
                      <div class="w-4 h-4 bg-gray-400 mr-3"></div>
                      {{ t('auth.signOut') }}
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Dashboard Header -->
      <div class="mb-8">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div>
            <h2 class="text-4xl font-black text-gray-900 uppercase tracking-tight">{{ t('dashboard.title') }}</h2>
            <p class="mt-1 text-xl text-gray-700 font-medium">{{ t('dashboard.subtitle') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <NuxtLink
              :to="localePath('/')"
              class="inline-flex items-center px-6 py-3 bg-green-500 text-white font-bold uppercase tracking-wide hover:bg-green-600 transition-colors"
            >
              <div class="w-4 h-4 bg-white mr-2"></div>
              {{ t('dashboard.uploadNewPDF') }}
            </NuxtLink>
          </div>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white border-2 border-gray-300 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500"></div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-black text-gray-900 uppercase tracking-wide">{{ t('dashboard.stats.totalPDFs') }}</p>
              <p class="text-2xl font-black text-gray-900">{{ userStats.totalPDFs }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white border-2 border-gray-300 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500"></div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-black text-gray-900 uppercase tracking-wide">{{ t('dashboard.stats.totalViews') }}</p>
              <p class="text-2xl font-black text-gray-900">{{ userStats.totalViews }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white border-2 border-gray-300 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500"></div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-black text-gray-900 uppercase tracking-wide">{{ t('dashboard.stats.activeLinks') }}</p>
              <p class="text-2xl font-black text-gray-900">{{ userStats.activeLinks }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- PDF List -->
      <div class="bg-white border-2 border-gray-300">
        <div class="px-4 py-5 sm:p-6">
          <div class="sm:flex sm:items-center sm:justify-between mb-6">
            <h3 class="text-2xl font-black text-gray-900 uppercase tracking-wide">{{ t('dashboard.yourPDFs') }}</h3>
            <div class="mt-3 sm:mt-0 sm:ml-4">
              <div class="flex rounded-md shadow-sm">
                <input
                  v-model="searchQuery"
                  type="text"
                  :placeholder="t('dashboard.searchPlaceholder')"
                  class="block w-full pl-3 pr-3 py-2 border-2 border-gray-300 text-sm placeholder-gray-500 focus:outline-none focus:border-green-500 font-medium"
                >
              </div>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="pendingPDFs" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-sm text-gray-600">{{ t('dashboard.loading') }}</p>
          </div>

          <!-- Empty State -->
          <div v-else-if="!filteredPDFs.length" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('dashboard.emptyState.title') }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ t('dashboard.emptyState.description') }}</p>
            <div class="mt-6">
              <NuxtLink
                :to="localePath('/')"
                class="inline-flex items-center px-6 py-3 bg-green-500 text-white font-bold uppercase tracking-wide hover:bg-green-600 transition-colors"
              >
                <div class="w-4 h-4 bg-white mr-2"></div>
                {{ t('dashboard.emptyState.uploadButton') }}
              </NuxtLink>
            </div>
          </div>

          <!-- PDF List -->
          <div v-else class="space-y-4">
            <div
              v-for="pdf in filteredPDFs"
              :key="pdf.id"
              class="border-2 border-gray-300 p-4 hover:bg-gray-50"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-black text-gray-900 truncate uppercase tracking-wide">{{ pdf.filename }}</p>
                      <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span class="font-medium">{{ formatDate(pdf.createdAt) }}</span>
                        <span class="font-medium">{{ pdf.views || 0 }} {{ t('dashboard.pdfItem.views') }}</span>
                        <span v-if="pdf.hasPassword" class="inline-flex items-center px-2 py-1 text-xs font-black uppercase tracking-wide bg-blue-500 text-white">
                          {{ t('dashboard.pdfItem.protected') }}
                        </span>
                        <span class="inline-flex items-center px-2 py-1 text-xs font-black uppercase tracking-wide"
                              :class="isExpired(pdf.expiresAt) ? 'bg-red-500 text-white' : 'bg-green-500 text-white'">
                          {{ isExpired(pdf.expiresAt) ? t('dashboard.pdfItem.expired') : t('dashboard.pdfItem.active') }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    @click="copyToClipboard(pdf.shareUrl)"
                    class="inline-flex items-center px-3 py-2 border-2 border-gray-300 text-sm font-medium text-gray-700 hover:text-green-700 hover:bg-green-50 hover:border-green-500 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    {{ t('dashboard.pdfItem.copyLink') }}
                  </button>
                  <a
                    :href="pdf.shareUrl"
                    target="_blank"
                    class="inline-flex items-center px-3 py-2 border-2 border-gray-300 text-sm font-medium text-gray-700 hover:text-blue-700 hover:bg-blue-50 hover:border-blue-500 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    {{ t('dashboard.pdfItem.view') }}
                  </a>
                  <button
                    @click="deletePDF(pdf.id)"
                    class="inline-flex items-center px-3 py-2 border-2 border-gray-300 text-sm font-medium text-gray-700 hover:text-red-700 hover:bg-red-50 hover:border-red-500 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    {{ t('dashboard.pdfItem.delete') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// i18n
const { t } = useI18n()
const localePath = useLocalePath()

// Authentication check
const { data, status, user, signOut } = useAuth()

// Redirect if not authenticated
watchEffect(() => {
  if (status.value === 'unauthenticated') {
    throw createError({
      statusCode: 401,
      statusMessage: 'Authentication required'
    })
  }
})

// Dropdown menu state
const isDropdownOpen = ref(false)

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

const handleSignOut = async () => {
  closeDropdown()
  await signOut()
}

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (event) => {
    const dropdownElement = event.target.closest('.relative')
    if (!dropdownElement) {
      isDropdownOpen.value = false
    }
  })
})

// Search functionality
const searchQuery = ref('')

// Fetch user's PDFs
const { data: userPDFsResponse, pending: pendingPDFs, refresh: refreshPDFs } = await useFetch('/api/pdf-links')

// Extract the actual PDF array from the API response
const userPDFs = computed(() => {
  if (!userPDFsResponse.value?.success || !userPDFsResponse.value?.data) return []
  return userPDFsResponse.value.data
})

// Computed properties
const filteredPDFs = computed(() => {
  if (!userPDFs.value || !Array.isArray(userPDFs.value)) return []
  
  return userPDFs.value.filter(pdf => 
    pdf.filename.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const userStats = computed(() => {
  if (!userPDFs.value || !Array.isArray(userPDFs.value)) return { totalPDFs: 0, totalViews: 0, activeLinks: 0 }
  
  const now = new Date()
  
  return {
    totalPDFs: userPDFs.value.length,
    totalViews: userPDFs.value.reduce((sum, pdf) => sum + (pdf.views || 0), 0),
    activeLinks: userPDFs.value.filter(pdf => new Date(pdf.expiresAt) > now).length
  }
})

// SEO
useHead({
  title: `${t('dashboard.title')} - PDFtoLink`,
  meta: [
    {
      name: 'description',
      content: t('dashboard.subtitle')
    },
    {
      name: 'robots',
      content: 'noindex, nofollow'
    }
  ]
})

// Utility functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const isExpired = (expiresAt) => {
  return new Date() > new Date(expiresAt)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // TODO: Show success notification using $t('dashboard.copySuccess')
  } catch (err) {
    console.error('Failed to copy:', err)
    // TODO: Show error notification using $t('dashboard.copyFailed')
  }
}

const deletePDF = async (pdfId) => {
  if (!confirm(t('dashboard.deleteConfirm'))) {
    return
  }
  
  try {
    await $fetch(`/api/pdf-links/${pdfId}`, {
      method: 'DELETE'
    })
    await refreshPDFs()
    // TODO: Show success notification using t('dashboard.deleteSuccess')
  } catch (error) {
    console.error('Failed to delete PDF:', error)
    // TODO: Show error notification using t('dashboard.deleteFailed')
  }
}
</script>