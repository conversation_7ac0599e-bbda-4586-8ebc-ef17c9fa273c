# PDFtoLink - Convert PDF Files to Shareable Links

PDFtoLink is a modern web application that allows users to upload PDF files and convert them into shareable links instantly. Built with Nuxt.js and designed for performance, security, and user experience.

## 🚀 Features

### Core Features
- **Instant PDF to Link Conversion**: Upload PDFs and get shareable links immediately
- **Multi-language Support**: English, Simplified Chinese, and Traditional Chinese
- **Drag & Drop Upload**: Intuitive file upload with progress indicators
- **Temporary Links**: Automatic expiration for privacy protection
- **Google OAuth Authentication**: Secure sign-in for advanced features
- **User Dashboard**: Manage uploaded PDFs and track analytics
- **Password Protection**: Secure sensitive documents with passwords
- **View Tracking**: Monitor who accesses your shared PDFs
- **Mobile Responsive**: Optimized for all devices

### Technical Features
- **Cloudflare R2 Storage**: Reliable and fast file storage
- **SEO Optimized**: Comprehensive meta tags and structured data
- **Progressive Enhancement**: Works without JavaScript
- **Security First**: Encrypted storage and access controls
- **Analytics Integration**: Built-in view tracking and reporting

## 🛠 Technology Stack

- **Frontend**: Nuxt.js 3, Vue.js 3, TailwindCSS
- **Backend**: <PERSON><PERSON> (Server-side rendering and API)
- **Authentication**: NextAuth.js with Google OAuth
- **Storage**: Cloudflare R2 (S3-compatible)
- **Internationalization**: @nuxtjs/i18n
- **Deployment**: Vercel/Netlify ready

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- Cloudflare R2 account
- Google OAuth credentials

### Environment Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd pdftolink
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Configure environment variables**
```bash
cp .env.example .env
```

Fill in your environment variables:

```env
# Authentication
NUXT_AUTH_SECRET=your-32-character-secret-key
NUXT_AUTH_ORIGIN=http://localhost:3000

# Google OAuth (get from Google Cloud Console)
NUXT_GOOGLE_CLIENT_ID=your-google-client-id
NUXT_GOOGLE_CLIENT_SECRET=your-google-client-secret

# Cloudflare R2 Storage
NUXT_R2_ACCOUNT_ID=your-r2-account-id
NUXT_R2_ACCESS_KEY_ID=your-r2-access-key-id
NUXT_R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
NUXT_R2_BUCKET_NAME=your-r2-bucket-name

# Site Configuration
NUXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Setting up Cloudflare R2

1. Create a Cloudflare account and enable R2
2. Create a new R2 bucket for PDF storage
3. Generate API tokens with R2 permissions
4. Configure CORS settings for your bucket

### Setting up Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`

## 🚀 Development

Start the development server:

```bash
pnpm dev
```

The application will be available at `http://localhost:3000`

### Available Scripts

```bash
# Development
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm preview      # Preview production build
pnpm generate     # Generate static site

# Code Quality
pnpm lint         # Run ESLint
pnpm type-check   # Run TypeScript checks
```

## 📁 Project Structure

```
pdftolink/
├── assets/          # Static assets (CSS, images)
├── components/      # Reusable Vue components
├── i18n/           # Internationalization files
│   └── locales/    # Translation files (en, zh-hans, zh-hant)
├── pages/          # Application pages/routes
│   ├── index.vue   # Homepage with upload
│   ├── dashboard.vue # User dashboard
│   └── view/       # PDF viewing pages
├── server/         # Server-side code
│   └── api/        # API endpoints
│       ├── auth/   # Authentication
│       ├── upload/ # File upload
│       ├── links/  # Link management
│       └── user/   # User operations
├── public/         # Public static files
└── nuxt.config.ts  # Nuxt configuration
```

## 🌐 API Endpoints

### File Upload
- `POST /api/upload/presign` - Get presigned upload URL
- `POST /api/links/generate` - Generate shareable link

### Link Management
- `GET /api/links/:id` - Get PDF data by link ID
- `POST /api/links/verify` - Verify password-protected link

### User Management
- `GET /api/user/pdfs` - Get user's uploaded PDFs
- `DELETE /api/user/pdfs/:id` - Delete user's PDF

### SEO & Utilities
- `GET /api/sitemap.xml` - XML sitemap
- `/robots.txt` - Robots.txt file

## 🔒 Security Features

- **File Encryption**: All uploaded files are encrypted at rest
- **Access Control**: Link-based permissions with optional passwords
- **Automatic Expiration**: Temporary links with configurable expiration
- **Rate Limiting**: Protection against abuse
- **CORS Protection**: Proper cross-origin resource sharing
- **Input Validation**: Comprehensive server-side validation

## 🌍 SEO & Performance

- **Server-Side Rendering**: Fast initial page loads
- **Meta Tags**: Comprehensive SEO optimization
- **Structured Data**: Schema.org markup for search engines
- **Sitemap**: Automatic XML sitemap generation
- **Image Optimization**: Automatic image compression
- **Code Splitting**: Optimized bundle sizes

## 📱 Mobile Support

- **Responsive Design**: Works perfectly on all screen sizes
- **Touch-Friendly**: Optimized for mobile interactions
- **Progressive Web App**: Can be installed on mobile devices
- **Offline Support**: Basic functionality works offline

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on every push

### Netlify

1. Connect repository to Netlify
2. Set build command: `pnpm build`
3. Set publish directory: `.output/public`
4. Configure environment variables

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY . .
RUN npm install -g pnpm
RUN pnpm install
RUN pnpm build
EXPOSE 3000
CMD ["node", ".output/server/index.mjs"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- **Documentation**: [Nuxt.js Docs](https://nuxt.com/)
- **Cloudflare R2**: [R2 Documentation](https://developers.cloudflare.com/r2/)
- **NextAuth.js**: [Auth Documentation](https://next-auth.js.org/)

## 🐛 Known Issues

- Authentication requires environment variables to be properly configured
- R2 bucket must have proper CORS settings
- Google OAuth requires HTTPS in production

## 📞 Support

For support, email <EMAIL> or create an issue in the GitHub repository.

---

Built with ❤️ using Nuxt.js