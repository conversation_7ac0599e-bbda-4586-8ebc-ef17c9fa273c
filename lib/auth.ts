import { Role } from "@prisma/client";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";

import prisma from "./db";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  advanced: {
    useSecureCookies: false
  },
  baseURL: process.env.AUTH_REDIRECT_BASE_URL,
  // Allow requests from the frontend development server
  trustedOrigins: [process.env.AUTH_REDIRECT_BASE_URL || "http://localhost:3000"],
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    // github: {
    //   clientId: process.env.GITHUB_CLIENT_ID || "",
    //   clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
    // },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
    },
  },
  user: {
    // additionalFields: {
    //   roles: {
    //     type: [Role.STUDENT, Role.CREATOR, Role.ADMIN],
    //     required: true,
    //     defaultValue: [Role.STUDENT],
    //   },
    // },
  },
});
