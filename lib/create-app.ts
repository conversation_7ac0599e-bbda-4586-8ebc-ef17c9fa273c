import { Hono } from "hono";
import { cors } from "hono/cors";
import type { auth } from "../lib/auth";

export type AuthType = {
  Variables: {
    user: typeof auth.$Infer.Session.user | null;
    session: typeof auth.$Infer.Session.session | null;
  };
};

export function createRouter() {
  return new Hono<{ Bindings: AuthType }>({
    strict: false,
  });
}

export default function createApp() {
  const app = createRouter();
  
  // Add CORS middleware
  app.use('*', cors({
    origin: [process.env.AUTH_REDIRECT_BASE_URL || "http://localhost:3003"],
    allowHeaders: ['Content-Type', 'Authorization', 'cf-connecting-ip', 'x-forwarded-for', 'x-real-ip', 'user-agent'],
    allowMethods: ['POST', 'GET', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true,
  }));

  return app;
}
