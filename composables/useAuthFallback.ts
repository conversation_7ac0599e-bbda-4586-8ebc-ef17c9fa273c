// Fallback auth implementation using direct API calls
export const useAuthFallback = () => {
  const user = ref(null)
  const status = ref('loading')
  const error = ref(null)

  // Check session on mount
  onMounted(async () => {
    await checkSession()
  })

  const checkSession = async () => {
    try {
      status.value = 'loading'
      const response = await $fetch('/api/auth/session', {
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })
      
      console.log('Session response:', response)
      
      if (response?.user) {
        user.value = response.user
        status.value = 'authenticated'
      } else {
        user.value = null
        status.value = 'unauthenticated'
      }
    } catch (e) {
      console.error('Session check failed:', e)
      user.value = null
      status.value = 'unauthenticated'
      error.value = e
    }
  }

  const signIn = async (provider: string) => {
    try {
      // Redirect to auth provider
      window.location.href = `/api/auth/signin/${provider}`
    } catch (e) {
      console.error('Sign in failed:', e)
      error.value = e
    }
  }

  const signOut = async () => {
    try {
      await $fetch('/api/auth/signout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })
      user.value = null
      status.value = 'unauthenticated'
      navigateTo('/')
    } catch (e) {
      console.error('Sign out failed:', e)
      error.value = e
    }
  }

  return {
    user: readonly(user),
    status: readonly(status),
    error: readonly(error),
    isPending: computed(() => status.value === 'loading'),
    signIn,
    signOut,
    checkSession
  }
}