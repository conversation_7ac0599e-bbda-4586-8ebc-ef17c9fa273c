import { authClient } from '~/lib/auth-client'

export const useAuth = () => {
  // Use better-auth's useSession hook directly
  const sessionData = authClient.useSession()
  
  // Manual state management since useSession() isn't working properly
  const manualSessionData = ref(null)
  
  // Wait for client-side hydration
  const isClient = process.client
  const isInitialized = ref(false)

  // Initialize session on client side
  onMounted(async () => {
    if (isClient) {
      try {
        console.log('Initializing auth session...')
        
        // Initialize auth client session
        const session = await authClient.getSession()
        console.log('Direct authClient.getSession() result:', session)
        console.log('Session data details:', session?.data)
        console.log('Session user:', session?.data?.user)
        
        // Store session data manually - better-auth returns nested structure
        if (session && session.data) {
          manualSessionData.value = session.data
          console.log('Stored manual session data:', manualSessionData.value)
        } else {
          manualSessionData.value = null
        }
        
        isInitialized.value = true
        console.log('Auth session initialized')
      } catch (error) {
        console.warn('Failed to initialize session:', error)
        isInitialized.value = true
      }
    }
  })

  // Computed properties for status and user
  const status = computed(() => {
    console.log('Auth session data (reactive):', sessionData.data?.value)
    console.log('Auth session data (manual):', manualSessionData.value)
    if (!isInitialized.value) return 'loading'
    if (sessionData.isPending?.value) return 'loading'
    
    // Use manual data if reactive data is not available
    const activeData = sessionData.data?.value || manualSessionData.value
    if (activeData?.user) return 'authenticated'
    return 'unauthenticated'
  })

  const user = computed(() => {
    const activeData = sessionData.data?.value || manualSessionData.value
    return activeData?.user || null
  })

  const error = computed(() => {
    return sessionData.error?.value || null
  })

  const data = computed(() => {
    return sessionData.data?.value || manualSessionData.value
  })

  const isPending = computed(() => {
    return !isInitialized.value || sessionData.isPending?.value
  })

  // Debug logging
  watchEffect(() => {
    if (isClient) {
      console.log('Auth state:', {
        isInitialized: isInitialized.value,
        isPending: sessionData.isPending?.value,
        hasData: !!sessionData.data?.value,
        hasUser: !!sessionData.data?.value?.user,
        status: status.value,
        user: user.value
      })
    }
  })

  // Sign in method
  const signIn = async (provider: string, options?: any) => {
    try {
      console.log(`Signing in with ${provider}...`)
      await authClient.signIn.social({
        provider,
        callbackURL: window.location.origin,
        ...options
      })
      
      // Refresh session data after sign in
      setTimeout(async () => {
        try {
          const newSession = await authClient.getSession()
          manualSessionData.value = newSession?.data || null
          console.log('Session refreshed after sign in:', newSession)
        } catch (e) {
          console.warn('Failed to refresh session after sign in:', e)
        }
      }, 1000)
    } catch (e) {
      console.error('Sign in error:', e)
      throw e
    }
  }

  // Sign out method
  const signOut = async () => {
    try {
      console.log('Signing out...')
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            console.log('Sign out successful')
            manualSessionData.value = null
            navigateTo('/')
          }
        }
      })
    } catch (e) {
      console.error('Sign out error:', e)
      // Clear session data even if sign out fails
      manualSessionData.value = null
      throw e
    }
  }

  return {
    data: readonly(data),
    status: readonly(status),
    user: readonly(user),
    error: readonly(error),
    isPending: readonly(isPending),
    signIn,
    signOut
  }
}