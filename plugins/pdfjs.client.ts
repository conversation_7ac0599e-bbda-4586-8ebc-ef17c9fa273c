export default defineNuxtPlugin(async () => {
  if (process.client) {
    // Dynamic import to avoid SSR issues
    const pdfjsLib = await import('pdfjs-dist')
    
    // Import worker URL
    const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.min.mjs?url')
    pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker.default
    
    // Configure PDF.js settings
    pdfjsLib.GlobalWorkerOptions.workerPort = null
    pdfjsLib.GlobalWorkerOptions.disableWorker = false
    
    return {
      provide: {
        pdfjs: pdfjsLib
      }
    }
  }
})