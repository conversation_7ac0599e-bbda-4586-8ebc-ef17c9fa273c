{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "outDir": "./dist", "rootDir": "./", "paths": {"@/*": ["./*"]}, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "lib/**/*", "routes/**/*"], "exclude": ["node_modules", "dist"]}