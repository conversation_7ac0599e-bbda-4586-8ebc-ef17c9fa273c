# Google OAuth 配置指南

## 当前错误
Google OAuth 返回 `Error 400: redirect_uri_mismatch` 错误，说明重定向 URI 配置不正确。

## 修复步骤

### 1. 访问 Google Cloud Console
- 打开: https://console.cloud.google.com/
- 登录你的 Google 账户

### 2. 选择项目
- 在顶部项目选择器中，选择对应的项目
- Client ID: `141813254179-7j96ngmfq6jiusc8voj9559pm3ar876p.apps.googleusercontent.com`

### 3. 导航到凭据页面
```
左侧菜单 → APIs & Services → Credentials
```

### 4. 编辑 OAuth 2.0 客户端 ID
- 找到你的 OAuth 2.0 客户端 ID
- 点击编辑按钮（铅笔图标）

### 5. 配置重定向 URI
在 "Authorized redirect URIs" 部分：

**必须添加的 URI（完全匹配）:**
```
http://localhost:3003/api/auth/callback/google
```

**注意事项:**
- ✅ 使用 `http://`（开发环境）
- ✅ 端口必须是 `3003`
- ✅ 路径必须是 `/api/auth/callback/google`
- ❌ 不要添加尾部斜杠
- ❌ 不要使用其他端口

### 6. 保存配置
- 点击 "SAVE" 按钮
- 等待配置生效（通常立即生效）

### 7. 测试认证
保存后立即测试：
1. 确保 better-auth 服务器运行在 localhost:3008
2. 确保 Nuxt 应用运行在 localhost:3003
3. 清除浏览器缓存
4. 访问: http://localhost:3003/auth-test
5. 点击 "Sign In with Google"
6. 完成授权流程

## 常见错误

### 错误配置示例:
```
❌ http://localhost:3000/api/auth/callback/google  (端口错误)
❌ http://localhost:3003/api/auth/callback/google/ (多余斜杠)
❌ https://localhost:3003/api/auth/callback/google  (协议错误)
❌ http://localhost:3003/auth/callback/google       (路径错误)
```

### 正确配置:
```
✅ http://localhost:3003/api/auth/callback/google
```

## 验证方法
配置保存后，重新尝试 OAuth 流程，应该不再出现 404 错误。