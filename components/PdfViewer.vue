<template>
  <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
    <!-- PDF Controls -->
    <div class="border-b border-gray-200 px-6 py-4 bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h2 class="text-lg font-medium text-gray-900">{{ filename }}</h2>
          <div class="text-sm text-gray-500">
            {{ views }} views • Expires: {{ formatDate(expiresAt) }}
          </div>
        </div>
        <div class="flex space-x-3">
          <button
            @click="copyToClipboard(shareUrl)"
            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Link
          </button>
          <a
            :href="downloadUrl"
            target="_blank"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download
          </a>
        </div>
      </div>
    </div>

    <!-- PDF Viewer Controls -->
    <div class="border-b border-gray-200 px-6 py-3 bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- Page Navigation -->
          <div class="flex items-center space-x-2">
            <button
              @click="goToPrevPage"
              :disabled="currentPage <= 1"
              class="p-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <div class="flex items-center space-x-2 text-sm">
              <input
                v-model.number="pageInput"
                @keyup.enter="goToPage"
                @blur="goToPage"
                type="number"
                :min="1"
                :max="totalPages"
                class="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
              >
              <span class="text-gray-500">of {{ totalPages }}</span>
            </div>
            
            <button
              @click="goToNextPage"
              :disabled="currentPage >= totalPages"
              class="p-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Zoom Controls -->
        <div class="flex items-center space-x-2">
          <button
            @click="zoomOut"
            class="p-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-100"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
            </svg>
          </button>
          
          <select
            v-model="scale"
            @change="updateScale"
            class="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="auto">Fit Width</option>
            <option value="page-fit">Fit Page</option>
            <option value="0.25">25%</option>
            <option value="0.5">50%</option>
            <option value="0.75">75%</option>
            <option value="1">100%</option>
            <option value="1.25">125%</option>
            <option value="1.5">150%</option>
            <option value="1.75">175%</option>
            <option value="2">200%</option>
            <option value="2.5">250%</option>
            <option value="3">300%</option>
          </select>
          
          <button
            @click="zoomIn"
            class="p-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-100"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        <p class="mt-2 text-gray-600">Loading PDF...</p>
        <div v-if="loadingProgress > 0" class="mt-2">
          <div class="w-48 bg-gray-200 rounded-full h-2">
            <div 
              class="bg-green-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${loadingProgress}%` }"
            ></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">{{ Math.round(loadingProgress) }}%</p>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
        <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
          <svg class="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-red-900 mb-2">Error Loading PDF</h3>
        <p class="text-red-700 mb-4">{{ error }}</p>
        <button
          @click="retryLoad"
          class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- PDF Canvas Container -->
    <div v-else class="relative">
      <div 
        ref="viewerContainer"
        class="overflow-auto bg-gray-100"
        style="height: 70vh;"
        @scroll="handleScroll"
      >
        <div 
          ref="pdfContainer"
          class="flex flex-col items-center p-4 space-y-4 min-h-full"
          style="width: 100%;"
        >
          <!-- Canvas elements will be inserted here -->
        </div>
      </div>
    </div>

    <!-- Download Fallback -->
    <div class="p-4 text-center border-t border-gray-200 bg-gray-50">
      <p class="text-sm text-gray-600 mb-2">Having trouble viewing the PDF?</p>
      <a
        :href="downloadUrl"
        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
        <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Download PDF
      </a>
    </div>
  </div>
</template>

<script setup>
let pdfjsLib = null

// Import PDF.js and setup on client side
onMounted(async () => {
  if (process.client) {
    // Add resize handler
    window.addEventListener('resize', handleResize)
    
    try {
      const pdfjs = await import('pdfjs-dist')
      pdfjsLib = pdfjs
      
      console.log('PDF.js loaded:', pdfjs.version)
      
      // Import worker directly
      const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.min.mjs?url')
      pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker.default
      
      console.log('Worker configured:', pdfjsWorker.default)
      
      // Wait for next tick to ensure DOM is fully ready
      await nextTick()
      
      // Load PDF after PDF.js is ready and DOM is ready
      loadPDF()
    } catch (err) {
      console.error('Error initializing PDF.js:', err)
      error.value = 'Failed to initialize PDF viewer'
    }
  }
})

const props = defineProps({
  downloadUrl: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  views: {
    type: Number,
    default: 0
  },
  expiresAt: {
    type: String,
    required: true
  },
  shareUrl: {
    type: String,
    required: true
  }
})

// Reactive state
const loading = ref(true)
const error = ref(null)
const loadingProgress = ref(0)
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref('auto')
const pageInput = ref(1)
const renderedPages = ref(new Set())

// Use shallowRef to avoid deep reactivity on PDF document
const pdfDoc = shallowRef(null)

// Template refs
const viewerContainer = ref(null)
const pdfContainer = ref(null)

// Handle window resize for auto-scale
let resizeTimeout = null
const handleResize = () => {
  if (resizeTimeout) clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    if (pdfDoc.value && (scale.value === 'auto' || scale.value === 'page-fit')) {
      renderAllPages()
    }
  }, 250) // Debounce resize events
}


onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', handleResize)
    if (resizeTimeout) clearTimeout(resizeTimeout)
  }
})

// Watch for scale changes
watch(scale, () => {
  if (pdfDoc.value) {
    renderAllPages()
  }
})

// Watch current page changes
watch(currentPage, (newPage) => {
  pageInput.value = newPage
})

const loadPDF = async () => {
  if (!pdfjsLib || !props.downloadUrl) return
  
  try {
    loading.value = true
    error.value = null
    loadingProgress.value = 0

    const loadingTask = pdfjsLib.getDocument({
      url: props.downloadUrl,
      // Add CORS headers if needed
      httpHeaders: {
        'Accept': 'application/pdf'
      }
    })

    // Track loading progress
    loadingTask.onProgress = (progress) => {
      if (progress.total > 0) {
        loadingProgress.value = (progress.loaded / progress.total) * 100
      }
    }

    const doc = await loadingTask.promise
    console.log('PDF loaded successfully:', doc, 'Type:', typeof doc, 'NumPages:', doc.numPages)
    
    // Store the document reference in shallowRef
    pdfDoc.value = doc
    totalPages.value = doc.numPages
    
    loading.value = false
    
    // Create placeholders for all pages and render the first page
    // Wait a bit to ensure DOM is ready
    await nextTick()
    await createPagePlaceholders()
    await renderPage(1)
    
  } catch (err) {
    console.error('Error loading PDF:', err)
    error.value = 'Failed to load PDF. Please check your connection and try again.'
    loading.value = false
  }
}

const renderPage = async (pageNum) => {
  if (!pdfDoc.value || !pdfContainer.value || renderedPages.value.has(pageNum)) return

  try {
    console.log('Rendering page:', pageNum, 'PDF doc type:', typeof pdfDoc.value, 'Has getPage:', !!pdfDoc.value.getPage)
    const page = await pdfDoc.value.getPage(pageNum)
    
    // Get device pixel ratio for high DPI displays
    const pixelRatio = window.devicePixelRatio || 1
    const baseScale = getScale()
    
    // Calculate viewports
    const displayViewport = page.getViewport({ scale: baseScale })
    const renderViewport = page.getViewport({ scale: baseScale * pixelRatio })

    // Create canvas with high resolution
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    // Set canvas internal resolution (for crisp rendering)
    canvas.width = renderViewport.width
    canvas.height = renderViewport.height
    
    // Set canvas display size (what users see)
    canvas.style.width = displayViewport.width + 'px'
    canvas.style.height = displayViewport.height + 'px'
    canvas.style.maxWidth = '100%'
    canvas.className = 'border border-gray-300 rounded block'
    
    // Improve canvas rendering quality
    canvas.style.imageRendering = 'crisp-edges'
    canvas.style.imageRendering = '-webkit-optimize-contrast'
    
    console.log('Canvas dimensions:', {
      canvasWidth: canvas.width,
      canvasHeight: canvas.height,
      displayWidth: canvas.style.width,
      displayHeight: canvas.style.height,
      pixelRatio,
      baseScale
    })

    // Find the existing page wrapper
    const existingWrapper = pdfContainer.value.querySelector(`[data-page="${pageNum}"]`)
    if (existingWrapper) {
      // Replace the placeholder with the rendered canvas
      const placeholder = existingWrapper.querySelector('div:first-child')
      if (placeholder) {
        placeholder.replaceWith(canvas)
      }
    } else {
      // Fallback: create new wrapper if placeholder doesn't exist
      const pageWrapper = document.createElement('div')
      pageWrapper.className = 'relative flex flex-col items-center mb-4'
      pageWrapper.setAttribute('data-page', pageNum.toString())
      
      const pageLabel = document.createElement('div')
      pageLabel.className = 'absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm'
      pageLabel.textContent = `Page ${pageNum}`
      
      pageWrapper.appendChild(canvas)
      pageWrapper.appendChild(pageLabel)
      pdfContainer.value.appendChild(pageWrapper)
    }

    // Render PDF page into canvas with high resolution viewport
    const renderContext = {
      canvasContext: context,
      viewport: renderViewport
    }

    await page.render(renderContext).promise
    renderedPages.value.add(pageNum)
    
  } catch (err) {
    console.error(`Error rendering page ${pageNum}:`, err)
  }
}

const renderAllPages = async () => {
  if (!pdfDoc.value) return
  
  // Clear existing renders
  pdfContainer.value.innerHTML = ''
  renderedPages.value.clear()
  
  // Render all pages
  for (let i = 1; i <= totalPages.value; i++) {
    await renderPage(i)
  }
}

const getScale = () => {
  if (scale.value === 'auto') {
    // Fit to container width
    const containerWidth = viewerContainer.value?.clientWidth || 800
    const padding = 32
    const maxWidth = containerWidth - padding
    
    // Use standard PDF page width in points (612 for Letter size)
    // PDF.js renders at 1 point = 1 pixel by default
    return Math.min(maxWidth / 612, 3) // Max 3x scale
  } else if (scale.value === 'page-fit') {
    // Fit to container dimensions
    const containerHeight = viewerContainer.value?.clientHeight || 600
    const containerWidth = viewerContainer.value?.clientWidth || 800
    const padding = 32
    const maxHeight = containerHeight - padding
    const maxWidth = containerWidth - padding
    
    // Calculate scale based on standard PDF dimensions
    const heightScale = maxHeight / 792 // 792 points for Letter height
    const widthScale = maxWidth / 612   // 612 points for Letter width
    return Math.min(heightScale, widthScale, 3) // Max 3x scale
  } else {
    return parseFloat(scale.value)
  }
}

const updateScale = () => {
  renderAllPages()
}

const zoomIn = () => {
  const currentScale = parseFloat(scale.value) || 1
  scale.value = Math.min(currentScale + 0.25, 3).toString()
}

const zoomOut = () => {
  const currentScale = parseFloat(scale.value) || 1
  scale.value = Math.max(currentScale - 0.25, 0.25).toString()
}

const goToPrevPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--
    await ensurePageRendered(currentPage.value)
    scrollToPage(currentPage.value)
  }
}

const goToNextPage = async () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    await ensurePageRendered(currentPage.value)
    scrollToPage(currentPage.value)
  }
}

const goToPage = async () => {
  const page = Math.max(1, Math.min(pageInput.value, totalPages.value))
  currentPage.value = page
  pageInput.value = page
  await ensurePageRendered(page)
  scrollToPage(page)
}

const ensurePageRendered = async (pageNum) => {
  if (!renderedPages.value.has(pageNum)) {
    await renderPage(pageNum)
  }
}

const createPagePlaceholders = async () => {
  if (!pdfDoc.value || !pdfContainer.value) return
  
  // Clear existing content
  pdfContainer.value.innerHTML = ''
  renderedPages.value.clear()
  
  // Create placeholder for each page
  for (let i = 1; i <= totalPages.value; i++) {
    const pageWrapper = document.createElement('div')
    pageWrapper.className = 'relative flex flex-col items-center mb-4'
    pageWrapper.setAttribute('data-page', i.toString())
    
    // Create a placeholder with estimated dimensions
    const placeholder = document.createElement('div')
    placeholder.className = 'border border-gray-300 rounded bg-gray-50 flex items-center justify-center'
    placeholder.style.width = '612px' // Standard PDF width
    placeholder.style.height = '792px' // Standard PDF height
    placeholder.style.maxWidth = '100%'
    
    // Add loading indicator
    const loadingText = document.createElement('div')
    loadingText.className = 'text-gray-500 text-sm'
    loadingText.textContent = `Loading page ${i}...`
    placeholder.appendChild(loadingText)
    
    // Add page number label
    const pageLabel = document.createElement('div')
    pageLabel.className = 'absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm'
    pageLabel.textContent = `Page ${i}`
    
    pageWrapper.appendChild(placeholder)
    pageWrapper.appendChild(pageLabel)
    pdfContainer.value.appendChild(pageWrapper)
  }
}

const scrollToPage = (pageNum) => {
  const pageElement = pdfContainer.value?.children[pageNum - 1]
  if (pageElement) {
    pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const handleScroll = () => {
  // Update current page based on scroll position
  if (!viewerContainer.value || !pdfContainer.value) return
  
  const scrollTop = viewerContainer.value.scrollTop
  const containerHeight = viewerContainer.value.clientHeight
  const children = pdfContainer.value.children
  
  for (let i = 0; i < children.length; i++) {
    const element = children[i]
    const rect = element.getBoundingClientRect()
    const containerRect = viewerContainer.value.getBoundingClientRect()
    
    if (rect.top <= containerRect.top + containerHeight / 2 && 
        rect.bottom >= containerRect.top + containerHeight / 2) {
      currentPage.value = i + 1
      
      // Render visible pages and nearby pages for smooth navigation
      renderVisiblePages()
      break
    }
  }
}

const renderVisiblePages = async () => {
  if (!viewerContainer.value || !pdfContainer.value) return
  
  const containerRect = viewerContainer.value.getBoundingClientRect()
  const children = pdfContainer.value.children
  
  for (let i = 0; i < children.length; i++) {
    const element = children[i]
    const rect = element.getBoundingClientRect()
    
    // Check if page is visible or near visible area
    const isVisible = rect.bottom >= containerRect.top && rect.top <= containerRect.bottom
    const isNearby = rect.bottom >= containerRect.top - containerRect.height && 
                    rect.top <= containerRect.bottom + containerRect.height
    
    if ((isVisible || isNearby) && !renderedPages.value.has(i + 1)) {
      await renderPage(i + 1)
    }
  }
}

const retryLoad = () => {
  loadPDF()
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // TODO: Show toast notification
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}
</script>