<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup>
// Wait for i18n to be ready before using locale head
const { locale } = useI18n()

// Use watchEffect to reactively update head when locale changes
watchEffect(() => {
  if (locale.value) {
    const localeHead = useLocaleHead()
    
    useHead({
      htmlAttrs: {
        lang: localeHead.value?.htmlAttrs?.lang || 'en',
        dir: localeHead.value?.htmlAttrs?.dir || 'ltr'
      },
      link: localeHead.value?.link || [],
      meta: localeHead.value?.meta || []
    })
  }
})
</script>