// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  ADMIN
  CREATOR
  STUDENT
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  roles         Role[]    @default([STUDENT])
  emailVerified Boolean
  image         String?
  sessions      Session[]
  accounts      Account[]
  fileUploads   FileUpload[]
  pdfLinks      PdfLink[]

  @@map("user")
}

model Course {
  id        String   @id @default(uuid())
  title     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model FileUpload {
  id        String   @id @default(uuid())
  fileName  String
  fileSize  Int
  fileType  String
  filePath  String
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  isAnonymous Boolean @default(false)
  uploadedAt DateTime @default(now())
  
  @@map("file_upload")
}

model PdfLink {
  id         String   @id @default(uuid())
  fileKey    String   // S3/R2 file key
  filename   String
  userId     String?
  user       User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  password   String?  // Optional password protection
  maxViews   Int?     // Optional view limit
  views      Int      @default(0)
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  lastViewed DateTime?
  isAuthenticated Boolean @default(false)
  
  // Access logs
  accessLogs PdfLinkAccessLog[]
  
  @@map("pdf_link")
}

model PdfLinkAccessLog {
  id        String   @id @default(uuid())
  linkId    String
  link      PdfLink  @relation(fields: [linkId], references: [id], onDelete: Cascade)
  ip        String
  userAgent String
  action    String   // 'view', 'download', etc.
  timestamp DateTime @default(now())
  
  @@map("pdf_link_access_log")
}
