-- CreateTable
CREATE TABLE "pdf_link" (
    "id" TEXT NOT NULL,
    "fileKey" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "userId" TEXT,
    "password" TEXT,
    "maxViews" INTEGER,
    "views" INTEGER NOT NULL DEFAULT 0,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastViewed" TIMESTAMP(3),
    "isAuthenticated" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "pdf_link_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pdf_link_access_log" (
    "id" TEXT NOT NULL,
    "linkId" TEXT NOT NULL,
    "ip" TEXT NOT NULL,
    "userAgent" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "pdf_link_access_log_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "pdf_link" ADD CONSTRAINT "pdf_link_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pdf_link_access_log" ADD CONSTRAINT "pdf_link_access_log_linkId_fkey" FOREIGN KEY ("linkId") REFERENCES "pdf_link"("id") ON DELETE CASCADE ON UPDATE CASCADE;