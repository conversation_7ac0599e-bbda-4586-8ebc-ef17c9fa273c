{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@aws-sdk/client-s3": "^3.709.0", "@aws-sdk/s3-request-presigner": "^3.709.0", "@nuxtjs/i18n": "^9.5.4", "@sidebase/nuxt-auth": "^0.9.4", "better-auth": "^1.2.9", "next-auth": "4.21.1", "nuxt": "^3.17.4", "pdfjs-dist": "^4.10.38", "uuid": "^11.0.3", "vue": "^3.5.15", "vue-router": "^4.5.1"}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903", "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.1", "tailwindcss": "^3.4.14"}}