{"name": "my-app", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "tsx src/index.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.0.0", "@hono/node-server": "^1.13.1", "@prisma/client": "^6.6.0", "bcrypt": "^5.1.1", "better-auth": "^1.2.5", "dotenv": "^16.5.0", "hono": "^4.7.6", "uuid": "^9.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^22.10.2", "@types/uuid": "^9.0.0", "prisma": "^6.6.0", "tsx": "^4.19.2", "typescript": "^5.7.2"}}