import locales from './i18n/locales'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: {
    enabled: true
  },
  nitro: {
    // devProxy: process.env.NODE_ENV === 'development' ? {
    //   '/api/': {
    //     target: `${process.env.NUXT_AUTH_API_URL}/api/`,
    //     changeOrigin: true
    //   }
    // } : undefined
  },
  routeRules: {
    // '/api/**': {
    //   proxy: {
    //     to: process.env.NUXT_AUTH_API_URL + '/api/**',
    //     cookieDomainRewrite: {
    //       '*': ''
    //     },
    //     cookiePathRewrite: {
    //       '*': '/'
    //     },
    //     headers: {
    //       'x-forwarded-host': process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3003'
    //     },
    //     onResponse: (event, response) => {
    //       console.log('on proxy response')
    //       // 处理 Set-Cookie 头
    //       const setCookie = response.headers.get('set-cookie');
    //       if (setCookie) {
    //         // 确保 cookie 设置正确
    //         const cookies = setCookie.split(',').map(cookie => {
    //           // 移除 SameSite=None 和 Secure 属性，以便在所有环境中工作
    //           return cookie.replace(/SameSite=None;?/g, '')
    //                        .replace(/Secure;?/g, '');
    //         });
    //         setResponseHeader(event, 'Set-Cookie', cookies);
    //       }
    //     }
    //   }
    // }
  },
  css: [
    '~/assets/css/main.css'
  ],
  modules: [
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss'
  ],
  auth: {
    baseURL: process.env.NUXT_AUTH_ORIGIN,
    provider: {
      type: 'authjs'
    }
  },
  runtimeConfig: {
    authSecret: process.env.NUXT_AUTH_SECRET,
    googleClientId: process.env.NUXT_GOOGLE_CLIENT_ID,
    googleClientSecret: process.env.NUXT_GOOGLE_CLIENT_SECRET,
    r2AccountId: process.env.NUXT_R2_ACCOUNT_ID,
    r2AccessKeyId: process.env.NUXT_R2_ACCESS_KEY_ID,
    r2SecretAccessKey: process.env.NUXT_R2_SECRET_ACCESS_KEY,
    r2BucketName: process.env.NUXT_R2_BUCKET_NAME,
    authApiUrl: process.env.NUXT_AUTH_API_URL || 'http://localhost:3008',
    public: {
      authUrl: process.env.NUXT_AUTH_ORIGIN || 'http://localhost:3003',
      apiUrl: process.env.NUXT_AUTH_API_URL || 'http://localhost:3008'
    }
  },
  i18n: {
    defaultLocale: 'en',
    locales,
    strategy: 'prefix_except_default',
    baseUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3003',
    lazy: true
  },
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'canonical', href: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3003' }
      ],
      meta: [
        { name: 'robots', content: 'index, follow' },
        { name: 'author', content: 'PDFtoLink' },
        { property: 'og:site_name', content: 'PDFtoLink' },
        { property: 'og:type', content: 'website' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:creator', content: '@pdftolink' },
        { name: 'theme-color', content: '#2563eb' }
      ],
      script: [
        // <script defer data-domain="pdftolink.app" src="https://click.pageview.click/js/script.js"></script>
        { src: 'https://click.pageview.click/js/script.js', defer: true, 'data-domain': 'pdftolink.app' },
        {
          type: 'application/ld+json',
          children: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebApplication',
            name: 'PDFtoLink',
            description: 'Convert PDF files to shareable links instantly. Upload your PDF and get a link to share with anyone, anywhere.',
            url: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3003',
            applicationCategory: 'UtilityApplication',
            operatingSystem: 'Web Browser',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD'
            },
            creator: {
              '@type': 'Organization',
              name: 'PDFtoLink'
            }
          })
        }
      ]
    }
  }
})
