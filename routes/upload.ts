import { auth } from "@/lib/auth";
import { createRouter } from "@/lib/create-app";
import prisma from "@/lib/db";
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

const router = createRouter();

// Generate presigned URL for file upload
router.post("/upload/presign", async (c) => {
  try {
    const body = await c.req.json();
    const { filename, contentType } = body;
    
    if (!filename || contentType !== 'application/pdf') {
      return c.json({ error: "Invalid file type. Only PDF files are allowed." }, 400);
    }
    
    // Configure S3 client for Cloudflare R2
    const s3Client = new S3Client({
      region: 'auto',
      endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });
    
    // Generate unique file key
    const fileExtension = filename.split('.').pop();
    const fileKey = `uploads/${uuidv4()}.${fileExtension}`;
    
    console.log(process.env.R2_BUCKET_NAME);
    // Create presigned URL for upload
    const command = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: fileKey,
      ContentType: contentType,
      Metadata: {
        originalFilename: filename,
        uploadedAt: new Date().toISOString(),
      }
    });
    
    const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 1800 }); // 30 minutes
    
    return c.json({
      success: true,
      data: {
        uploadUrl,
        fileKey,
        expiresIn: 3600
      }
    });
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    return c.json({ error: "Failed to generate upload URL" }, 500);
  }
});

// Record file upload
router.post("/upload/record", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    const body = await c.req.json();
    const { fileName, fileKey } = body;

    if (!fileName || !fileKey) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    const uploadRecord = await prisma.fileUpload.create({
      data: {
        fileName,
        fileSize: 0,
        fileType: "application/pdf",
        filePath: fileKey,
        userId: session?.user?.id || null,
        isAnonymous: !session?.user?.id,
      },
    });

    return c.json({
      success: true,
      uploadId: uploadRecord.id,
      isAnonymous: uploadRecord.isAnonymous,
    });
  } catch (error) {
    console.error("Upload record error:", error);
    return c.json({ error: "Failed to record upload" }, 500);
  }
});

// Get upload history (for authenticated users)
router.get("/upload/history", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (!session?.user?.id) {
      return c.json({ error: "Authentication required" }, 401);
    }

    const uploads = await prisma.fileUpload.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        uploadedAt: "desc",
      },
      select: {
        id: true,
        fileName: true,
        fileSize: true,
        fileType: true,
        uploadedAt: true,
      },
    });

    return c.json({ uploads });
  } catch (error) {
    console.error("Upload history error:", error);
    return c.json({ error: "Failed to fetch upload history" }, 500);
  }
});

export default router;