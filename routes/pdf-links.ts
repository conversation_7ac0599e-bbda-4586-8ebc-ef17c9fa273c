import { auth } from "@/lib/auth";
import { createRouter } from "@/lib/create-app";
import prisma from "@/lib/db";
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import bcrypt from 'bcrypt';

const router = createRouter();

// Create a PDF link
router.post("/pdf-links/generate", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    const body = await c.req.json();
    const { fileKey, filename, password, maxViews, customExpiration, isAuthenticated } = body;

    if (!fileKey || !filename) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    // Set expiration based on authentication status
    const now = new Date();
    const expirationTime = isAuthenticated 
      ? new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days for authenticated users
      : new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours for anonymous users

    // Hash password if provided
    const hashedPassword = password ? await bcrypt.hash(password, 10) : null;

    // Create PDF link in database
    const pdfLink = await prisma.pdfLink.create({
      data: {
        fileKey,
        filename,
        userId: session?.user?.id || null,
        password: hashedPassword,
        maxViews: maxViews || (isAuthenticated ? null : 100),
        expiresAt: customExpiration ? new Date(customExpiration) : expirationTime,
        isAuthenticated: !!session?.user?.id,
      },
    });

    const baseUrl = process.env.PUBLIC_SITE_URL;
    const shareableUrl = `${baseUrl}/view/${pdfLink.id}`;

    return c.json({
      success: true,
      data: {
        linkId: pdfLink.id,
        shareableUrl,
        expiresAt: pdfLink.expiresAt.toISOString(),
        maxViews: pdfLink.maxViews,
      }
    });
  } catch (error) {
    console.error('Error creating PDF link:', error);
    return c.json({ error: "Failed to create PDF link" }, 500);
  }
});

// Get PDF link data
router.get("/pdf-links/:id", async (c) => {
  try {
    const linkId = c.req.param('id');
    
    if (!linkId) {
      return c.json({ error: "Link ID is required" }, 400);
    }

    const pdfLink = await prisma.pdfLink.findUnique({
      where: { id: linkId },
      include: {
        user: {
          select: { id: true, name: true }
        }
      }
    });

    if (!pdfLink) {
      return c.json({ error: "Link not found or expired" }, 404);
    }

    // Check if link has expired
    if (new Date() > pdfLink.expiresAt) {
      // Delete expired link
      await prisma.pdfLink.delete({
        where: { id: linkId }
      });
      return c.json({ error: "Link has expired" }, 410);
    }

    // Check view limits
    if (pdfLink.maxViews && pdfLink.views >= pdfLink.maxViews) {
      return c.json({ error: "View limit exceeded" }, 429);
    }

    // Check if password is required
    if (pdfLink.password) {
      return c.json({
        success: false,
        requiresPassword: true,
        data: {
          filename: pdfLink.filename,
          hasPassword: true,
          views: pdfLink.views,
          maxViews: pdfLink.maxViews,
          expiresAt: pdfLink.expiresAt.toISOString()
        }
      });
    }

    // Configure S3 client for Cloudflare R2
    const s3Client = new S3Client({
      region: 'auto',
      endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });

    // Generate presigned URL for download
    const command = new GetObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: pdfLink.fileKey,
    });

    const downloadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour

    // Update view count and last viewed
    const updatedLink = await prisma.pdfLink.update({
      where: { id: linkId },
      data: {
        views: { increment: 1 },
        lastViewed: new Date(),
      }
    });

    // Log access
    const clientIP = c.req.header('cf-connecting-ip') || 
                    c.req.header('x-forwarded-for') || 
                    c.req.header('x-real-ip') || 
                    'unknown';
    const userAgent = c.req.header('user-agent') || 'unknown';

    await prisma.pdfLinkAccessLog.create({
      data: {
        linkId,
        ip: clientIP,
        userAgent,
        action: 'view',
      }
    });

    return c.json({
      success: true,
      data: {
        filename: pdfLink.filename,
        downloadUrl,
        views: updatedLink.views,
        expiresAt: pdfLink.expiresAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching PDF link:', error);
    return c.json({ error: "Failed to generate download URL" }, 500);
  }
});

// Verify password for protected PDF links
router.post("/pdf-links/verify", async (c) => {
  try {
    const body = await c.req.json();
    const { linkId, password } = body;

    if (!linkId || !password) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    const pdfLink = await prisma.pdfLink.findUnique({
      where: { id: linkId }
    });

    if (!pdfLink) {
      return c.json({ error: "Link not found" }, 404);
    }

    if (!pdfLink.password) {
      return c.json({ error: "Link is not password protected" }, 400);
    }

    // Check if link has expired
    if (new Date() > pdfLink.expiresAt) {
      await prisma.pdfLink.delete({
        where: { id: linkId }
      });
      return c.json({ error: "Link has expired" }, 410);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, pdfLink.password);
    
    if (!isPasswordValid) {
      return c.json({ error: "Invalid password" }, 401);
    }

    // Configure S3 client for Cloudflare R2
    const s3Client = new S3Client({
      region: 'auto',
      endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });

    // Generate presigned URL for download
    const command = new GetObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: pdfLink.fileKey,
    });

    const downloadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour

    // Update view count and last viewed
    const updatedLink = await prisma.pdfLink.update({
      where: { id: linkId },
      data: {
        views: { increment: 1 },
        lastViewed: new Date(),
      }
    });

    // Log access
    const clientIP = c.req.header('cf-connecting-ip') || 
                    c.req.header('x-forwarded-for') || 
                    c.req.header('x-real-ip') || 
                    'unknown';
    const userAgent = c.req.header('user-agent') || 'unknown';

    await prisma.pdfLinkAccessLog.create({
      data: {
        linkId,
        ip: clientIP,
        userAgent,
        action: 'password_verified',
      }
    });

    return c.json({
      success: true,
      data: {
        filename: pdfLink.filename,
        downloadUrl,
        views: updatedLink.views,
        expiresAt: pdfLink.expiresAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error verifying password:', error);
    return c.json({ error: "Failed to verify password" }, 500);
  }
});

// Get user's PDF links (for authenticated users)
router.get("/pdf-links", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (!session?.user?.id) {
      return c.json({ error: "Authentication required" }, 401);
    }

    const pdfLinks = await prisma.pdfLink.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        filename: true,
        views: true,
        maxViews: true,
        expiresAt: true,
        createdAt: true,
        lastViewed: true,
        password: true, // Will be null or string, frontend can check !!password
      },
    });

    const baseUrl = process.env.PUBLIC_SITE_URL;
    
    return c.json({ 
      success: true,
      data: pdfLinks.map(link => ({
        ...link,
        hasPassword: !!link.password,
        password: undefined, // Don't send actual password hash
        shareUrl: `${baseUrl}/view/${link.id}`,
      }))
    });
  } catch (error) {
    console.error("PDF links fetch error:", error);
    return c.json({ error: "Failed to fetch PDF links" }, 500);
  }
});

// Delete PDF link (for authenticated users)
router.delete("/pdf-links/:id", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (!session?.user?.id) {
      return c.json({ error: "Authentication required" }, 401);
    }

    const linkId = c.req.param('id');
    
    const pdfLink = await prisma.pdfLink.findUnique({
      where: { id: linkId }
    });

    if (!pdfLink) {
      return c.json({ error: "Link not found" }, 404);
    }

    if (pdfLink.userId !== session.user.id) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    await prisma.pdfLink.delete({
      where: { id: linkId }
    });

    return c.json({ success: true });
  } catch (error) {
    console.error("PDF link deletion error:", error);
    return c.json({ error: "Failed to delete PDF link" }, 500);
  }
});

export default router;